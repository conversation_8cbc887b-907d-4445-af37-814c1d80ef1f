import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import * as routeManager from '@/utils/routeManager'
import { type AuthRouteConfig } from '@/dynamicRoutes'

// 用户信息接口
export interface User {
  userId: string
  userNo: string
  userName: string
  userEmail: string
  englishName: string
  role: string[]
}

// 权限元数据接口
export interface RouteMeta {
  requiresAuth?: boolean
  roles?: string[]
}

// 认证状态接口
interface AuthState {
  user: User | null
  isLoggedIn: boolean
  // permissions: string[]
  userRoutesConfig: AuthRouteConfig[]
  areDynamicRoutesRegistered: boolean
  login: (userData: User) => void
  logout: () => void
  loadUserResources: () => Promise<void>
  initializeAuth: () => void
}

// 创建认证store
export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      // 初始状态
      user: null,
      isLoggedIn: false,
      permissions: [],
      userRoutesConfig: [],
      areDynamicRoutesRegistered: false,

      // 登录操作
      login: (userData: User) => {
        set({
          user: userData,
          isLoggedIn: true,
          // permissions: userPermissions,
          areDynamicRoutesRegistered: false,
        })
      },

      // 登出操作
      logout: () => {
        set({
          user: null,
          isLoggedIn: false,
          // permissions: [],
          userRoutesConfig: [],
          areDynamicRoutesRegistered: false,
        })
        routeManager.clearRoutes()
      },

      // 模拟加载用户资源
      loadUserResources: async () => {
        // 模拟API调用延迟
        await new Promise((resolve) => setTimeout(resolve, 1000))

        const mockRoutes: AuthRouteConfig[] = [
          {
            path: '/',
            component: '',
          } as AuthRouteConfig,
          {
            path: '/dashboard',
            layout: 'default',
            component: 'DashboardPage',
            meta: { isHomePage: true, requiresAuth: true },
          } as AuthRouteConfig,
          {
            path: '/menu-management',
            layout: 'default',
            component: 'Systems/MenuManagementPage',
            meta: { requiresAuth: true },
          } as AuthRouteConfig,
          {
            path: '/user-info',
            layout: 'default',
            component: 'user/UserInfo',
            meta: { requiresAuth: true },
          } as AuthRouteConfig,
          {
            path: '/404',
            layout: 'blank',
            component: 'error/NotFoundPage',
          } as AuthRouteConfig,
          {
            path: '/403',
            layout: 'blank',
            component: 'UnauthorizedPage',
          } as AuthRouteConfig,
        ]

        set({ userRoutesConfig: mockRoutes })

        // 延迟 300 ms, 修改 areDynamicRoutesRegistered: true
        setTimeout(() => {
          set({ areDynamicRoutesRegistered: true })
        }, 300)
      },

      // 初始化认证状态
      initializeAuth: () => {
        const { isLoggedIn, areDynamicRoutesRegistered } = get()
        // 如果用户已登录但动态路由未注册，设置为已注册状态
        if (isLoggedIn && !areDynamicRoutesRegistered) {
          get().loadUserResources()
        }
      },
    }),
    {
      name: 'auth-storage', // 持久化存储的键名
      partialize: (state) => ({
        isLoggedIn: state.isLoggedIn,
        user: state.user,
        // permissions: state.permissions,
      }), // 只持久化部分状态
    }
  )
)
