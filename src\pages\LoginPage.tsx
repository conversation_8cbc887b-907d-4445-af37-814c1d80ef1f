import { useAuthStore } from '@/stores/authStore'
import { Button } from 'antd'
import { useLocation, useNavigate } from 'react-router-dom'

function LoginPage() {
  // const { loadUserResources, login } = useAuthStore()
  const navigate = useNavigate()
  const location = useLocation()

  const handleLogin = async () => {
    // login({
    //   email: 'admin',
    //   name: 'admin',
    //   role: ['admin'],
    //   token: 'token',
    //   id: '1',
    // })
    // await loadUserResources()

    // 判断 location.state 是否存在
    if (location.state && location.state.redirect) {
      navigate(location.state.redirect, { replace: true })
      return
    }
    navigate('/dashboard', { replace: true })
  }

  return (
    <div>
      <div>LoginPage</div>

      <Button type='primary' onClick={handleLogin}>
        {' '}
        Login{' '}
      </Button>
    </div>
  )
}

export default LoginPage
